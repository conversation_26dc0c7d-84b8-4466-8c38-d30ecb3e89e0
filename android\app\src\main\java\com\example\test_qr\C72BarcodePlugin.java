package com.example.test_qr;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.NonNull;

import com.rscja.barcode.BarcodeDecoder;
import com.rscja.barcode.BarcodeFactory;
import com.rscja.deviceapi.entity.BarcodeEntity;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.common.EventChannel;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

import java.util.HashMap;
import java.util.Map;

/**
 * Plugin Flutter pour intégrer le scanner de codes-barres C72
 * Basé sur le SDK officiel Chainway
 */
public class C72BarcodePlugin implements FlutterPlugin, MethodCallHandler, ActivityAware {
    private static final String TAG = "C72BarcodePlugin";
    private static final String CHANNEL = "c72_barcode_scanner";
    private static final String EVENT_CHANNEL = "c72_barcode_scanner/events";

    private MethodChannel channel;
    private EventChannel eventChannel;
    private EventChannel.EventSink eventSink;
    private Context context;
    private Activity activity;
    
    // SDK C72
    private BarcodeDecoder barcodeDecoder;
    private boolean isInitialized = false;
    private boolean isScanning = false;
    private Handler mainHandler;

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), CHANNEL);
        channel.setMethodCallHandler(this);
        
        eventChannel = new EventChannel(flutterPluginBinding.getBinaryMessenger(), EVENT_CHANNEL);
        eventChannel.setStreamHandler(new EventChannel.StreamHandler() {
            @Override
            public void onListen(Object arguments, EventChannel.EventSink events) {
                eventSink = events;
            }

            @Override
            public void onCancel(Object arguments) {
                eventSink = null;
            }
        });
        
        context = flutterPluginBinding.getApplicationContext();
        mainHandler = new Handler(Looper.getMainLooper());
        
        Log.d(TAG, "Plugin attaché au moteur Flutter");
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        switch (call.method) {
            case "initialize":
                initialize(result);
                break;
            case "isAvailable":
                result.success(isC72Available());
                break;
            case "startScan":
                startScan(result);
                break;
            case "stopScan":
                stopScan(result);
                break;
            case "isScanning":
                result.success(isScanning);
                break;
            case "close":
                close(result);
                break;
            case "getDeviceInfo":
                getDeviceInfo(result);
                break;
            default:
                result.notImplemented();
                break;
        }
    }

    /**
     * Initialise le scanner C72
     */
    private void initialize(Result result) {
        try {
            if (activity == null) {
                result.error("NO_ACTIVITY", "Activity non disponible", null);
                return;
            }

            // Initialiser le décodeur de codes-barres
            barcodeDecoder = BarcodeFactory.getInstance().getBarcodeDecoder();
            
            if (barcodeDecoder == null) {
                result.error("INIT_FAILED", "Impossible d'obtenir le décodeur de codes-barres", null);
                return;
            }

            // Ouvrir le scanner
            boolean openResult = barcodeDecoder.open(activity);
            Log.d(TAG, "Résultat d'ouverture du scanner: " + openResult);

            if (!openResult) {
                result.error("OPEN_FAILED", "Impossible d'ouvrir le scanner C72", null);
                return;
            }

            // Configurer le callback de décodage
            barcodeDecoder.setDecodeCallback(new BarcodeDecoder.DecodeCallback() {
                @Override
                public void onDecodeComplete(BarcodeEntity barcodeEntity) {
                    handleBarcodeResult(barcodeEntity);
                }
            });

            isInitialized = true;
            Log.d(TAG, "Scanner C72 initialisé avec succès");
            result.success(true);

        } catch (Exception e) {
            Log.e(TAG, "Erreur lors de l'initialisation: " + e.getMessage(), e);
            result.error("INIT_ERROR", "Erreur d'initialisation: " + e.getMessage(), null);
        }
    }

    /**
     * Vérifie si le C72 est disponible
     */
    private boolean isC72Available() {
        try {
            // Tenter de créer une instance du décodeur
            BarcodeDecoder testDecoder = BarcodeFactory.getInstance().getBarcodeDecoder();
            return testDecoder != null;
        } catch (Exception e) {
            Log.e(TAG, "C72 non disponible: " + e.getMessage());
            return false;
        }
    }

    /**
     * Démarre le scan
     */
    private void startScan(Result result) {
        try {
            if (!isInitialized || barcodeDecoder == null) {
                result.error("NOT_INITIALIZED", "Scanner non initialisé", null);
                return;
            }

            if (isScanning) {
                result.success(true); // Déjà en cours de scan
                return;
            }

            barcodeDecoder.startScan();
            isScanning = true;
            Log.d(TAG, "Scan démarré");
            result.success(true);

        } catch (Exception e) {
            Log.e(TAG, "Erreur lors du démarrage du scan: " + e.getMessage(), e);
            result.error("START_SCAN_ERROR", "Erreur de démarrage: " + e.getMessage(), null);
        }
    }

    /**
     * Arrête le scan
     */
    private void stopScan(Result result) {
        try {
            if (!isInitialized || barcodeDecoder == null) {
                result.error("NOT_INITIALIZED", "Scanner non initialisé", null);
                return;
            }

            if (!isScanning) {
                result.success(true); // Déjà arrêté
                return;
            }

            barcodeDecoder.stopScan();
            isScanning = false;
            Log.d(TAG, "Scan arrêté");
            result.success(true);

        } catch (Exception e) {
            Log.e(TAG, "Erreur lors de l'arrêt du scan: " + e.getMessage(), e);
            result.error("STOP_SCAN_ERROR", "Erreur d'arrêt: " + e.getMessage(), null);
        }
    }

    /**
     * Ferme le scanner
     */
    private void close(Result result) {
        try {
            if (barcodeDecoder != null) {
                if (isScanning) {
                    barcodeDecoder.stopScan();
                    isScanning = false;
                }
                barcodeDecoder.close();
                barcodeDecoder = null;
            }
            
            isInitialized = false;
            Log.d(TAG, "Scanner fermé");
            result.success(true);

        } catch (Exception e) {
            Log.e(TAG, "Erreur lors de la fermeture: " + e.getMessage(), e);
            result.error("CLOSE_ERROR", "Erreur de fermeture: " + e.getMessage(), null);
        }
    }

    /**
     * Obtient les informations sur l'appareil
     */
    private void getDeviceInfo(Result result) {
        Map<String, Object> deviceInfo = new HashMap<>();
        deviceInfo.put("model", "Chainway C72");
        deviceInfo.put("scanner_type", "Integrated 2D Barcode Scanner");
        deviceInfo.put("sdk_version", "DeviceAPI_ver20230228");
        deviceInfo.put("is_available", isC72Available());
        deviceInfo.put("is_initialized", isInitialized);
        deviceInfo.put("is_scanning", isScanning);
        
        result.success(deviceInfo);
    }

    /**
     * Gère les résultats de scan
     */
    private void handleBarcodeResult(BarcodeEntity barcodeEntity) {
        mainHandler.post(() -> {
            try {
                if (eventSink == null) return;

                Map<String, Object> scanResult = new HashMap<>();
                
                if (barcodeEntity.getResultCode() == BarcodeDecoder.DECODE_SUCCESS) {
                    String barcodeData = barcodeEntity.getBarcodeData();
                    Log.d(TAG, "Code-barres scanné: " + barcodeData);
                    
                    scanResult.put("success", true);
                    scanResult.put("data", barcodeData);
                    scanResult.put("timestamp", System.currentTimeMillis());
                    scanResult.put("scanner_type", "c72_integrated");
                    
                    // Arrêter le scan après un scan réussi
                    isScanning = false;
                    
                } else {
                    Log.w(TAG, "Échec du décodage: " + barcodeEntity.getResultCode());
                    scanResult.put("success", false);
                    scanResult.put("error", "Échec du décodage");
                    scanResult.put("error_code", barcodeEntity.getResultCode());
                }
                
                eventSink.success(scanResult);
                
            } catch (Exception e) {
                Log.e(TAG, "Erreur lors du traitement du résultat: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        channel.setMethodCallHandler(null);
        eventChannel.setStreamHandler(null);
        
        // Nettoyer les ressources
        if (barcodeDecoder != null) {
            try {
                if (isScanning) {
                    barcodeDecoder.stopScan();
                }
                barcodeDecoder.close();
            } catch (Exception e) {
                Log.e(TAG, "Erreur lors du nettoyage: " + e.getMessage());
            }
        }
        
        Log.d(TAG, "Plugin détaché du moteur Flutter");
    }

    @Override
    public void onAttachedToActivity(@NonNull ActivityPluginBinding binding) {
        activity = binding.getActivity();
        Log.d(TAG, "Plugin attaché à l'activité");
    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {
        activity = null;
    }

    @Override
    public void onReattachedToActivityForConfigChanges(@NonNull ActivityPluginBinding binding) {
        activity = binding.getActivity();
    }

    @Override
    public void onDetachedFromActivity() {
        activity = null;
        Log.d(TAG, "Plugin détaché de l'activité");
    }
}
