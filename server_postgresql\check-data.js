const { pool } = require('./config/database');
require('dotenv').config();

// Script pour vérifier les données dans PostgreSQL
async function checkData() {
  const client = await pool.connect();
  
  try {
    console.log('🔍 Vérification des données dans PostgreSQL...\n');
    
    // 1. Compter le nombre total d'enregistrements
    const countResult = await client.query('SELECT COUNT(*) FROM qr_scans');
    const totalScans = countResult.rows[0].count;
    console.log(`📊 Nombre total de scans: ${totalScans}`);
    
    if (totalScans === '0') {
      console.log('❌ Aucune donnée trouvée dans la table qr_scans');
      return;
    }
    
    // 2. Afficher les 10 derniers scans
    console.log('\n📱 Les 10 derniers scans:');
    console.log('=' .repeat(80));
    
    const recentScans = await client.query(`
      SELECT 
        id,
        qr_data,
        scan_timestamp,
        scanner_type,
        client_ip,
        created_at
      FROM qr_scans 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    recentScans.rows.forEach((scan, index) => {
      console.log(`\n${index + 1}. ID: ${scan.id}`);
      console.log(`   QR Data: ${scan.qr_data}`);
      console.log(`   Scanner: ${scan.scanner_type || 'N/A'}`);
      console.log(`   IP Client: ${scan.client_ip || 'N/A'}`);
      console.log(`   Timestamp: ${scan.scan_timestamp || scan.created_at}`);
    });
    
    // 3. Statistiques par type de scanner
    console.log('\n📈 Statistiques par type de scanner:');
    console.log('=' .repeat(50));
    
    const statsResult = await client.query(`
      SELECT 
        scanner_type,
        COUNT(*) as count,
        MAX(created_at) as last_scan
      FROM qr_scans 
      GROUP BY scanner_type
      ORDER BY count DESC
    `);
    
    statsResult.rows.forEach(stat => {
      console.log(`   ${stat.scanner_type || 'Non spécifié'}: ${stat.count} scans`);
      console.log(`   Dernier scan: ${stat.last_scan}`);
    });
    
    // 4. Scans d'aujourd'hui
    console.log('\n📅 Scans d\'aujourd\'hui:');
    console.log('=' .repeat(30));
    
    const todayResult = await client.query(`
      SELECT COUNT(*) as today_count
      FROM qr_scans 
      WHERE DATE(created_at) = CURRENT_DATE
    `);
    
    console.log(`   Nombre: ${todayResult.rows[0].today_count}`);
    
    // 5. Afficher le contenu détaillé du dernier scan
    if (recentScans.rows.length > 0) {
      console.log('\n🔍 Détails du dernier scan:');
      console.log('=' .repeat(40));
      
      const lastScan = recentScans.rows[0];
      const detailResult = await client.query(`
        SELECT * FROM qr_scans WHERE id = $1
      `, [lastScan.id]);
      
      const detail = detailResult.rows[0];
      Object.keys(detail).forEach(key => {
        let value = detail[key];
        if (typeof value === 'object' && value !== null) {
          value = JSON.stringify(value, null, 2);
        }
        console.log(`   ${key}: ${value}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Erreur lors de la vérification:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Exécuter la vérification
if (require.main === module) {
  checkData()
    .then(() => {
      console.log('\n✅ Vérification terminée');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Erreur:', error);
      process.exit(1);
    });
}

module.exports = { checkData };
