import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import '../services/scanner_service.dart';
import '../services/api_service.dart';

/// Écran de scan universel qui s'adapte au type d'appareil
class UniversalScannerScreen extends StatefulWidget {
  final void Function(String, {Map<String, dynamic>? metadata}) onScan;

  const UniversalScannerScreen({super.key, required this.onScan});

  @override
  State<UniversalScannerScreen> createState() => _UniversalScannerScreenState();
}

class _UniversalScannerScreenState extends State<UniversalScannerScreen> {
  ScannerService? _scannerService;
  bool _isLoading = true;
  bool _isScanning = false;
  String? _errorMessage;
  String? _scannerInfo;

  @override
  void initState() {
    super.initState();
    _initializeScanner();
  }

  @override
  void dispose() {
    _scannerService?.dispose();
    super.dispose();
  }

  /// Initialise le scanner approprié selon l'appareil
  Future<void> _initializeScanner() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Obtenir l'instance du scanner approprié
      _scannerService = await ScannerFactory.getInstance();

      // Vérifier la disponibilité
      final isAvailable = await _scannerService!.isAvailable();
      if (!isAvailable) {
        throw Exception('Scanner non disponible sur cet appareil');
      }

      // Configurer l'info du scanner
      _scannerInfo = _getScannerInfo();

      // Écouter les résultats de scan
      _scannerService!.scanResults.listen(
        (scanResult) {
          _handleScanResult(scanResult);
        },
        onError: (error) {
          _handleScanError(error);
        },
      );

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Erreur d\'initialisation: $e';
      });
    }
  }

  /// Obtient les informations sur le scanner
  String _getScannerInfo() {
    switch (_scannerService!.scannerType) {
      case ScannerType.c72Device:
        return 'Scanner C72 - Matériel intégré';
      case ScannerType.mobileCamera:
        return 'Scanner caméra - Mobile standard';
    }
  }

  /// Démarre le scan
  Future<void> _startScan() async {
    if (_scannerService == null || _isScanning) return;

    try {
      setState(() {
        _isScanning = true;
        _errorMessage = null;
      });

      await _scannerService!.startScan();
    } catch (e) {
      setState(() {
        _isScanning = false;
        _errorMessage = 'Erreur de démarrage: $e';
      });
    }
  }

  /// Arrête le scan
  Future<void> _stopScan() async {
    if (_scannerService == null || !_isScanning) return;

    try {
      await _scannerService!.stopScan();
      setState(() {
        _isScanning = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur d\'arrêt: $e';
      });
    }
  }

  /// Gère les résultats de scan
  void _handleScanResult(ScanResult scanResult) {
    print('Scan reçu: ${scanResult.data} via ${scanResult.scannerType}');

    // Appeler le callback avec les métadonnées
    widget.onScan(scanResult.data, metadata: scanResult.metadata);

    // Arrêter le scan et fermer l'écran
    _stopScan();
    Navigator.pop(context);
  }

  /// Gère les erreurs de scan
  void _handleScanError(dynamic error) {
    setState(() {
      _errorMessage = 'Erreur de scan: $error';
      _isScanning = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Scanner QR/Code-barres"),
        actions: [
          if (_scannerInfo != null)
            IconButton(
              icon: const Icon(Icons.info),
              onPressed: () => _showScannerInfo(),
            ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// Construit le corps de l'écran selon l'état
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Initialisation du scanner...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red.shade400),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializeScanner,
              child: const Text('Réessayer'),
            ),
          ],
        ),
      );
    }

    // Affichage selon le type de scanner
    return _buildScannerView();
  }

  /// Construit la vue du scanner selon le type
  Widget _buildScannerView() {
    switch (_scannerService!.scannerType) {
      case ScannerType.mobileCamera:
        return _buildCameraScannerView();
      case ScannerType.c72Device:
        return _buildC72ScannerView();
    }
  }

  /// Vue pour le scanner caméra
  Widget _buildCameraScannerView() {
    return MobileScanner(
      onDetect: (capture) {
        final code = capture.barcodes.first.rawValue;
        if (code != null && !_isScanning) {
          setState(() => _isScanning = true);

          // Simuler l'émission via le service pour la cohérence
          if (_scannerService is MobileCameraScannerService) {
            (_scannerService as MobileCameraScannerService).emitScanResult(
              code,
            );
          }
        }
      },
    );
  }

  /// Vue pour le scanner C72
  Widget _buildC72ScannerView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.qr_code_scanner,
            size: 120,
            color: _isScanning ? Colors.green : Colors.grey,
          ),
          const SizedBox(height: 32),
          Text(
            _isScanning
                ? 'Scanner C72 actif\nPointez vers un code-barres ou QR code'
                : 'Appuyez sur le bouton pour démarrer le scan',
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 18),
          ),
          const SizedBox(height: 16),
          if (_isScanning) const CircularProgressIndicator(),
        ],
      ),
    );
  }

  /// Bouton d'action flottant
  Widget? _buildFloatingActionButton() {
    if (_scannerService?.scannerType != ScannerType.c72Device) {
      return null; // Pas de bouton pour la caméra
    }

    return FloatingActionButton.extended(
      onPressed: _isScanning ? _stopScan : _startScan,
      icon: Icon(_isScanning ? Icons.stop : Icons.play_arrow),
      label: Text(_isScanning ? 'Arrêter' : 'Scanner'),
      backgroundColor: _isScanning ? Colors.red : Colors.green,
    );
  }

  /// Affiche les informations sur le scanner
  void _showScannerInfo() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Informations Scanner'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Type: $_scannerInfo'),
                const SizedBox(height: 8),
                Text('État: ${_isScanning ? "Actif" : "Inactif"}'),
                if (_scannerService?.scannerType == ScannerType.c72Device) ...[
                  const SizedBox(height: 8),
                  const Text('Capacités: QR codes, codes-barres, RFID UHF'),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }
}
