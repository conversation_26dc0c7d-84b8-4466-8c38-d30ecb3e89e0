# Serveur API de Test pour QR Scanner

Ce serveur Node.js simple permet de tester l'intégration API de votre application Flutter QR Scanner.

## Installation

1. Assurez-vous d'avoir Node.js installé sur votre système
2. Naviguez vers le dossier `server_example`
3. Installez les dépendances :

```bash
npm install
```

## Démarrage

```bash
npm start
```

Ou pour le développement avec rechargement automatique :

```bash
npm run dev
```

Le serveur démarrera sur `http://localhost:3000`

## Endpoints disponibles

### GET `/api/health`
Vérification de santé du serveur
- **Réponse** : Status du serveur avec timestamp

### POST `/api/qr-scan`
Reçoit les données du QR scanné depuis l'application Flutter
- **Body** : `{ "qr_data": "...", "timestamp": "...", "device_info": "..." }`
- **Réponse** : Confirmation de réception avec ID unique

### GET `/api/qr-data` (Debug)
Récupère toutes les données QR reçues
- **Réponse** : Liste de toutes les données stockées

### DELETE `/api/qr-data` (Debug)
Vide le stockage des données QR
- **Réponse** : Confirmation de suppression

## Test avec l'application Flutter

1. Démarrez ce serveur
2. Lancez votre application Flutter
3. Générez un QR code dans l'app
4. Scannez-le - les données seront automatiquement envoyées au serveur
5. Vérifiez les logs du serveur pour voir les données reçues

## Logs

Le serveur affiche dans la console :
- Les requêtes de health check
- Les données QR reçues avec tous les détails
- Les erreurs éventuelles

## Personnalisation

Vous pouvez modifier `server.js` pour :
- Ajouter une base de données réelle
- Implémenter l'authentification
- Ajouter des validations spécifiques
- Traiter les données QR selon vos besoins
