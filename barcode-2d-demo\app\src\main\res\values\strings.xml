<resources>
    <string name="app_name">barcode-2d-demo</string>

    <string name="action_settings">Settings</string>
    <string name="title_scan">Test</string>
    <string name="title_barcode">2DSettings</string>
    <string name="title_setting">AppSettings</string>
    <string name="title_function">Function</string>

    <string name="title_opt">Function</string>
    <string name="title_1d">Barcode1D</string>
    <string name="title_2d">Barcode2D</string>
    <string name="title_2ds">Barcode2D(S)</string>
    <string name="title_14443A">14443A</string>

    <string name="title_15693">15693</string>
    <string name="title_14443B">14443B</string>
    <string name="title_uhf">UHF</string>
    <string name="title_lf_id">IDCard</string>
    <string name="title_lf_at">Animal</string>
    <string name="title_lf_ht">HiTag</string>
    <string name="title_lf_hdx">HDX</string>
    <string name="title_lf_em4450">EM4450</string>
    <string name="title_lf_hid">HID</string>
    <string name="title_lf_tinyAniTag">TinyAniTag</string>
    <string name="title_hex">Hex</string>
    <string name="title_decimal">Decimal</string>
    <string name="title_Open">Enable Scanner</string>
    <string name="title_EndMark">End mark</string>
    <string name="title_Enter">Enter</string>
    <string name="title_Tab">TAB</string>
    <string name="title_Extras">Extras</string>
    <string name="title_Prefix">Prefix</string>
    <string name="title_Broadcast">Broadcast name</string>
    <string name="title_Broadcast_key">Key</string>
    <string name="title_continuous">Continuous scan</string>
    <string name="title_interval_time">Interval</string>
    <string name="title_time_out">Time out</string>
    <string name="title_time_ms">ms</string>
    <string name="title_time_second">s</string>
    <string name="title_KeyCode">Keycode</string>
    <string name="title_Suffix">Suffix</string>
    <string name="msg_ime_error">Unable to open, please enter the system interface manually!</string>
    <string name="title_tips">Tips</string>
    <string name="msg_tips">In order to normal use this feature, please set the default input method for Android Keyboard</string>
    <string name="btn_Cancel">Cancel</string>
    <string name="btn_Settings">Setting</string>
    <string name="title_operator">Process mode</string>
    <string name="title_Keyboard">Keyboard input</string>
    <string name="title_Keyboard2">Keyboard input2</string>
    <string name="title_clipboard">Clipboard</string>
    <string name="title_broadcast">BroadcastReceiver</string>


    <string name="msg_clipboard_succ">Have been copied to the clipboard</string>

    <string name="update_msg_unmount">Unable to download the installation file, please check the SD card is mounted</string>
    <string name="update_msg_checking">Please wait...</string>
    <string name="update_msg_diag_title">Message</string>
    <string name="update_msg_curr_new">You currently have is the latest version</string>
    <string name="update_msg_version_info_fail">Unable to get updated version information</string>
    <string name="update_title_ok">confirm</string>
    <string name="update_title_cancel">cancel</string>
    <string name="update_msg_diag_title_two">Software version update</string>
    <string name="update_title_update_now">Now</string>
    <string name="update_title_update_last">Last</string>
    <string name="update_msg_updateing">Downloading</string>
    <string name="init_device_fail">init device failed</string>

    <string name="msg_barcode_set">Bar code scan setup</string>

    <string name="msg_barcode_ck">Release key off scan</string>
    <string name="msg_light_ck">Decoding Illumination</string>
    <string name="title_dialog_code_types">Choose bar code types</string>
    <string name="title_close">Close</string>

    <string name="title_Sound">Success Sound</string>
    <string name="title_SoundFail">Failure Sound</string>
    <string name="title_Vibrate">Vibrate</string>
    <string name="title_general">General Settings</string>
    <string name="title_GroupSeparator">Remove group separator</string>
    <string name="title_interceptScanKey">block scan key</string>
    <string name="title_AuxiliaryLight">Scan Auxiliary Light</string>
    <string name="msg_on_off_code_types">Enable/disable decoders</string>
    <string name="msg_data_format">Data Format</string>
    <string name="msg_data_format_RFID">RFID</string>
    <string name="msg_data_format_Barcode">Barcode</string>
    <string name="msg_data_hex">Send as hex</string>

    <string name="title_start_index">Remove the front number of characters</string>
    <string name="title_end_index">Remove back the number of characters</string>
    <string name="title_filter">Filter data</string>

    <string name="title_code_params">Barcode parameters</string>

    <string name="title_rcd" translatable="false">Report Check Digit</string>
    <string name="title_cuetua" translatable="false">Convert UPC-E to UPC-A</string>
    <string name="title_length1" translatable="false">Length1</string>
    <string name="title_length2" translatable="false">Length2</string>

    <string name="title_egs1" translatable="false">Enable GS1-128</string>
    <string name="title_ei1" translatable="false">Enable ISBT 128</string>
    <string name="title_cit" translatable="false">Check ISBT Table</string>
    <string name="title_cdv" translatable="false">Check Digit Verification</string>
    <string name="title_fac" translatable="false">Full ASCII Conversion</string>
    <string name="title_cctc" translatable="false">Convert Code 39 to Code 32</string>
    <string name="title_c32p" translatable="false">Code 32 Prefix</string>

    <string name="title_ciote" translatable="false">Convert I 2 of 5 to EAN 13</string>
    <string name="title_ce" translatable="false">CLSI Editing</string>
    <string name="title_ne" translatable="false">NOTIS Editing</string>
    <string name="title_tmcd" translatable="false">Transmit MSI Check Digit</string>
    <string name="title_tccd" translatable="false">Transmit Code 11 Check Digit(s)</string>


    <string name="title_reader_params">Reader settings</string>

    <string name="title_trig">KEYS TRIGGER</string>

    <!-- TODO: Remove or change this placeholder text -->
    <string name="hello_blank_fragment">Hello blank fragment</string>

    <string name="msg_confirm">Confirm</string>
    <string name="msg_cancel">Cancel</string>

    <string name="title_enable">Enable</string>
    <string name="title_disable">Disable</string>
    <string name="title_settings">Set</string>
    <string name="title_scan1">Scan</string>
    <string name="title_stopScan">Stop</string>
    <string name="title_mode">Mode</string>
    <string name="title_function1">Function</string>
    <string name="title_external_app_test">Test</string>
    <string name="continuous">continuous</string>
    <string name="interval">interval</string>
    <string name="title_Register">RegisterBroadcast</string>
    <!--霍尼扫描头配置 -->

    <string name="hello_world">Hello world!</string>
    <string name="menu_settings">Settings</string>
    <string name="get_last_image">Get Last Image</string>
    <string name="set_scan_key">Set Scan Key</string>
    <string name="scan_mode">Scan Mode</string>
    <string name="image_mode">Image Mode</string>
    <string name="set_scan_key_mode">Set SCAN Key Mode</string>
    <string name="scan_for_picture_or_press_menu_for_options">Press SCAN button to take picture or select menu for options.\n\n</string>
    <string name="scan_for_start_scanning_or_press_menu_for_options">Press SCAN button to start barcode scanning or select menu for options.\n\n</string>
    <string name="scan_to_set_scan_key">Press SCAN button to configure scan key.\n\n</string>
    <string name="results_hint">Data displayed here</string>
    <string name="length_hint">Length displayed here</string>
    <string name="codeid_hint">CodeID displayed here</string>
    <string name="aimid_hint">AimID displayed here</string>
    <string name="modifier_hint">Modifier displayed here</string>
    <string name="label_results">Data:</string>
    <string name="label_length">Length:</string>
    <string name="label_codeid">CodeID:</string>
    <string name="label_aimid">AimID:</string>
    <string name="label_modifier">Modifier:</string>
    <string name="label_scan_mode">Scan Mode</string>
    <string name="label_image_mode">Image Mode</string>
    <string name="label_data_results">Data: (barcode_data)\nLength: (length)\nCodeId: (code_id)\nAimId: (aim_id)\nModifier: (modifier)</string>

    <!-- Lights Settings Strings -->
    <string name="pref_ic_lights_control_settings">Lights Control Settings</string>
    <string name="pref_ic_illumination_on_summary">Enable/Disable Illumination</string>
    <string name="pref_ic_illumination_on_settings">Illumination Enable</string>
    <string name="pref_ic_aimer_on_summary">Enable/Disable Aimer</string>
    <string name="pref_ic_aimer_on_settings">Aimer Enable</string>

    <!-- Symbology Config Strings -->
    <string name="sym_configuration">Symbology Configuration</string>
    <string name="sym_enable">Enable</string>
    <string name="sym_enable_summary">Select to enable symbology</string>
    <string name="sym_advanced_preferences">Advanced Preferences</string>
    <string name="sym_advanced_preferences_summary">Configure advanced settings (min, max, etc.)</string>
    <string name="sym_minlength_title">Min Length</string>
    <string name="sym_minlength_summary">Configure Minimum Length</string>
    <string name="sym_maxlength_title">Max Length</string>
    <string name="sym_maxlength_summary">Configure Maximum Length</string>
    <string name="symbology_select_to_configure_advanced_properties">Select to config advanced properties</string>
    <string name="sym_enable_disable">Enable/Disable Symbologies</string>
    <string name="sym_uncheck_to_disable">Currently enabled, uncheck to disable</string>
    <string name="sym_check_to_enable">Currently disabled, check to enable</string>
    <string name="scanning_preferences">Scanning Preferences</string>

    <string name="title_lights_config">Lights Configuration</string>
    <string name="title_lights_config_summary">For configuring lights mode</string>

    <!-- Lights Mode Strings -->
    <string-array name="lights_config_values_titles">
        <item>Illum/Aimer Off</item>
        <item>Aimer Only</item>
        <item>Illum Only</item>
        <item>Alternating</item>
        <item>Concurrent</item>
    </string-array>


    <!-- Wait For Decode Strings -->
    <string name="decode_wait_for_decode_config">WaitForDecode Config</string>
    <string name="title_wait_for_decode_config_summary">Wait for single or multiple barcodes</string>
    <string-array name="wait_for_decode_config_values_titles">
        <item>Wait For Single</item>
        <item>Wait For Multiple</item>
    </string-array>

    <!-- File Save Type Strings -->
    <string-array name="file_save_type_titles">
        <item>.pgm</item>
        <item>.png</item>
        <item>.raw</item>
    </string-array>

    <!-- Common Symbology Config Strings -->
    <string name="sybology_configuration">Symbology Configuration</string>
    <string name="symbology_enable_disable_configuration">Enable/Disable</string>
    <string name="symbology_enable_disable_configuration_summary">For enabling or disabling symbologies</string>
    <string name="symbology_advanced_configuration">Advanced Configuration</string>
    <string name="symbology_advanced_configuration_summary">For configuring min, max, flags, etc.</string>
    <string name="sym_postal_list_title">Postal Symbology Configuration</string>
    <string name="sym_postal_list_summary">For configuring 2D postal symbologies</string>

    <!-- Postal Strings -->
    <string-array name="postal_sym_entries">
        <item>No Postal</item>
        <item>Austrialan Post</item>
        <item>British Post</item>
        <item>Canadian Post</item>
        <item>Dutch Post</item>
        <item>Japan Post</item>
        <item>Planet Code</item>
        <item>Post Net</item>
        <item>US Postals</item>
        <item>UPU 4 State</item>
        <item>USPS 4 State</item>
    </string-array>
    <string name="sym_koreapost">China Post</string>
    <string name="sym_chinapost">Korea Post</string>

    <!-- OCR Tempate Strings -->
    <string-array name="ocr_template_entries">
        <item>User Defined</item>
        <item>Passport</item>
        <item>ISBN</item>
        <item>Price Field</item>
        <item>MicrE-13 B</item>
    </string-array>

    <!-- OCR Mode Strings -->
    <string-array name="ocr_mode_entries">
        <item>Off</item>
        <item>Normal Video</item>
        <item>Inverse</item>
        <item>Both</item>
    </string-array>

    <!-- Symbology Titles -->
    <!-- sym_name_title -->
    <!-- SYM_AZTEC = 0 -->
    <string name="sym_aztec_title">Aztec Code</string>
    <!-- SYM_CODABAR = 1 -->
    <string name="sym_codabar_title">Codabar</string>
    <!-- SYM_CODE11 = 2 -->
    <string name="sym_code11_title">Code11</string>
    <!-- SYM_CODE128 = 3 -->
    <string name="sym_code128_title">Code128</string>
    <!-- SYM_CODE39 = 4 -->
    <string name="sym_code39_title">Code39</string>
    <!-- SYM_CODE49 = 5 -->
    <string name="sym_code49_title">Code49</string>
    <!-- SYM_CODE93 = 6 -->
    <string name="sym_code93_title">Code93</string>
    <!-- SYM_COMPOSITE = 7 -->
    <string name="sym_composite_title">Composite</string>
    <!-- SYM_DATAMATRIX = 8 -->
    <string name="sym_datamatrix_title">Data Matrix</string>
    <!-- SYM_EAN8 = 9 -->
    <string name="sym_ean8_title">EAN8</string>
    <!-- SYM_EAN13 = 10 -->
    <string name="sym_ean13_title">EAN13</string>
    <!-- SYM_INT25 = 11 -->
    <string name="sym_int25_title">INT25</string>
    <!-- SYM_MAXICODE = 12 -->
    <string name="sym_maxicode_title">Maxicode</string>
    <!-- SYM_MICROPDF = 13 -->
    <string name="sym_micropdf_title">MicroPDF</string>
    <!-- SYM_OCR = 14 -->
    <string name="sym_ocr_title">OCR</string>
    <!-- SYM_PDF417 = 15 -->
    <string name="sym_pdf417_title">PDF417</string>
    <!-- SYM_POSTNET = 16 -->
    <string name="sym_postnet_title">Postnet</string>
    <!-- SYM_QR = 17 -->
    <string name="sym_qr_title">QR</string>
    <!-- SYM_RSS = 18 -->
    <string name="sym_rss_title">RSS</string>
    <!-- SYM_UPCA = 19 -->
    <string name="sym_upca_title">UPC-A</string>
    <!-- SYM_UPCE0 = 20 -->
    <string name="sym_upce0_title">UPC-E0</string>
    <!-- SYM_UPCE1 = 21 -->
    <string name="sym_upce1_title">UPC-E1</string>
    <!-- SYM_ISBT = 22 -->
    <string name="sym_isbt_title">ISBT</string>
    <!-- SYM_BPO = 23 -->
    <string name="sym_bpo_title">BPO</string>
    <!-- SYM_CANPOST = 24 -->
    <string name="sym_canpost_title">Canadian Post</string>
    <!-- SYM_AUSPOST = 25 -->
    <string name="sym_auspost_title">Australian Post</string>
    <!-- SYM_IATA25 = 26 -->
    <string name="sym_iata25_title">IATA25</string>
    <!-- SYM_CODABLOCK = 27 -->
    <string name="sym_codablock_title">Codeblock</string>
    <!-- SYM_JAPOST = 28 -->
    <string name="sym_japost_title">Japan Post</string>
    <!-- SYM_PLANET = 29 -->
    <string name="sym_planet_title">Planet</string>
    <!-- SYM_DUTCHPOST = 30 -->
    <string name="sym_dutchpost_title">Dutch Post</string>
    <!-- SYM_MSI = 31 -->
    <string name="sym_msi_title">MSI</string>
    <!-- SYM_TLCODE39 = 32 -->
    <string name="sym_tlcode39_title">TLC Code39</string>
    <!-- SYM_TRIOPTIC = 33 -->
    <string name="sym_trioptic_title">Trioptic</string>
    <!-- SYM_CODE32 = 34 -->
    <string name="sym_code32_title">Code32</string>
    <!-- SYM_STRT25 = 35 -->
    <string name="sym_strt25_title">STRT25</string>
    <!-- SYM_MATRIX25 = 36 -->
    <string name="sym_matrix25_title">Matrix 2 or 5</string>
    <!-- SYM_PLESSEY = 37 -->
    <string name="sym_plessey_title">Plessey</string>
    <!-- SYM_CHINAPOST = 38 -->
    <string name="sym_chinapost_title">China Post</string>
    <!-- SYM_KOREAPOST = 39 -->
    <string name="sym_koreapost_title">Korean Post</string>
    <!-- SYM_TELEPEN = 40 -->
    <string name="sym_telepen_title">Telepen</string>
    <!-- SYM_CODE16K = 41 -->
    <string name="sym_code16k_title">Code16K</string>
    <!-- SYM_POSICODE = 42 -->
    <string name="sym_posicode_title">PosiCode</string>
    <!-- SYM_COUPONCODE = 43 -->
    <string name="sym_couponcode_title">Coupon Code</string>
    <!-- SYM_USPS4CB = 44 -->
    <string name="sym_usps4cb_title">USPS4CB</string>
    <!-- SYM_IDTAG = 45 -->
    <string name="sym_idtag_title">ID Tag</string>
    <!-- SYM_LABEL = 46 -->
    <string name="sym_label_title">Label</string>
    <!-- SYM_GS1_128 = 47 -->
    <string name="sym_gs1_128_title">GS1 128</string>
    <!-- SYM_HANXIN = 48 -->
    <string name="sym_hanxin_title">Hanxin</string>
    <!-- SYM_GRIDMATRIX = 49 -->
    <string name="sym_gridmatrix_title">Grid Matrix</string>
    <!-- SYM_POSTALS = 50 -->
    <string name="sym_postals_title">Postals</string>
    <!-- SYM_US_POSTALS1 = 51 -->
    <string name="sym_us_postals1_title">US Postals</string>

    <!-- Symbology Flag Strings -->
    <string name="sym_check_enable_title">Check Enable</string>
    <string name="sym_start_stop_transmit_enable_title">Start/Stop Transit</string>
    <string name="sym_codabar_concatenate_enable_title">Codabar Concatenate</string>
    <string name="sym_append_enable_title">Append</string>
    <string name="sym_fullascii_enable_title">Start/Stop Transit</string>
    <string name="sym_composite_upc_enable_title">Composite UPC</string>
    <string name="sym_check_transmit_enable_title">Check Transmit</string>
    <string name="sym_num_transmit_enable_title">Num Transmit</string>
    <string name="sym_addenda_separator_enable_title">Addenda Separator</string>
    <string name="sym_2_digit_addenda_enable_title">2 Digit Addenda</string>
    <string name="sym_5_digit_addenda_enable_title">5 Digit Addenda</string>
    <string name="sym_addenda_required_enable_title">Addenda Required</string>
    <string name="sym_upce0_expanded_enable_title">UPCE0 Exanded</string>
    <string name="sym_rss_rsl_enable_title">RSL RSS Enable</string>
    <string name="sym_rss_rse_enable_title">RSE RSS Enable</string>
    <string name="sym_telepen_old_style_enable_title">Telepen Old Style</string>
    <string name="enable_all_sym">Enable All Sym</string>
    <string name="disable_all_sym">Disable All Sym</string>
    <string name="about">About</string>
    <string name="reset_settings">Reset Settings</string>

    <!-- Image Capture Activity String -->
    <string name="title_activity_image_capture">ImageCaptureActivity</string>

    <!-- Decode Time Limit Strings -->
    <string name="decode_time_limit_title">Decode Time Limit</string>
    <string name="decode_time_limit_summary">Used to set time limit decoder has to decode a region of interestt</string>

    <!-- Decode Wait For Decode Strings -->
    <string name="decode_use_wait_for_decode_with_timeout_only_title">WaitForDecode(timeout)</string>
    <string name="decode_use_wait_for_decode_with_timeout_only_summaryon">Uncheck to use WaitForDecode(timeout, DecodeResult)</string>
    <string name="decode_use_wait_for_decode_with_timeout_only_summaryoff">Check to use WaitForDecode(timeout)</string>

    <!-- Exposure Mode Strings -->
    <string name="title_exposure_mode">Exposure Mode</string>
    <string name="title_exposure_mode_summary">For setting exposure mode</string>
    <string-array name="exposure_mode_titles">
        <item>Fixed</item>
        <item>Auto</item>
    </string-array>

    <!-- Decode Centering (Windowing) Strings -->
    <string name="decode_centering_mode_list_title">Mode Configuration</string>
    <string name="decode_centering_mode_list_summary">For Centering Mode</string>
    <string-array name="decode_centering_mode_entries">
        <item>Off</item>
        <item>Around Aimer</item>
        <item>Field Of View</item>
        <item>Sub Image</item>
    </string-array>

    <!-- Aus Interpret Mode Strings -->
    <string name="sym_auspost_interpret_mode_list_title">Aus Post Interpret Mode</string>
    <string name="sym_auspost_interpret_mode_list_summary">For Setting Interpret Mode</string>
    <string-array name="sym_auspost_interpret_mode_entries">
        <item>None</item>
        <item>Numeric N Table</item>
        <item>Alphanumeric C Table</item>
        <item>Combination N and C Tablese</item>
    </string-array>
    <string name="time_out">TimeOut：</string>
    <string name="save">Save</string>
    <string name="title_factoty">Factoty data reset</string>



    <string-array name="lights_config_values">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
    </string-array>

    <!-- Exposure Modes -->
    <string-array name="exposure_mode_values">
        <item name="fixed">0</item>
        <item name="auto">2</item>
    </string-array>

    <!-- Postal Sym Values - used to determine what is actually being set -->
    <string-array name="postal_sym_values">
        <item name="no_postals">0</item>
        <item name="aus_post">1</item>
        <item name="british_post">7</item>
        <item name="canadian">30</item>
        <item name="dutch_post">4</item>
        <item name="japan_post">3</item>
        <item name="planet_code">5</item>
        <item name="post_net">6</item>
        <item name="us_postals">29</item>
        <item name="upu_4_state">9</item>
        <item name="usps_4_state">10</item>
    </string-array>

    <string-array name="decode_centering_mode_values">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
    </string-array>
</resources>
