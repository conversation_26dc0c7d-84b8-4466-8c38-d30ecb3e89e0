import '../services/scanner_service.dart';
import '../services/c72_scanner_service.dart';

/// Configuration pour le scanner
class ScannerConfig {
  // Configuration pour forcer un type de scanner spécifique
  static bool forceC72Scanner = false; // Mettre à true pour forcer l'utilisation du C72
  
  /// Détecte automatiquement le type de scanner à utiliser
  static Future<ScannerService> getScanner() async {
    if (forceC72Scanner) {
      return C72ScannerServiceImpl();
    }
    
    // Logique de détection automatique
    if (await _isC72Device()) {
      return C72ScannerServiceImpl();
    } else {
      return MobileCameraScannerService();
    }
  }
  
  /// Détecte si l'appareil est un C72
  static Future<bool> _isC72Device() async {
    try {
      // Méthode 1: Vérifier le modèle de l'appareil
      // TODO: Implémenter la vérification du modèle
      
      // Méthode 2: Tester la disponibilité du SDK C72
      final c72Scanner = C72ScannerServiceImpl();
      final isAvailable = await c72Scanner.isAvailable();
      await c72Scanner.dispose();
      
      return isAvailable;
    } catch (e) {
      print('Erreur lors de la détection du C72: $e');
      return false;
    }
  }
  
  /// Configuration pour les tests
  static void enableC72ForTesting() {
    forceC72Scanner = true;
  }
  
  static void disableC72ForTesting() {
    forceC72Scanner = false;
  }
}
