                        -HD:\flutter_windows_3.29.3-stable\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DC<PERSON>KE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\test_qr\build\app\intermediates\cxx\RelWithDebInfo\4058424q\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\test_qr\build\app\intermediates\cxx\RelWithDebInfo\4058424q\obj\x86
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BC:\Users\<USER>\test_qr\android\app\.cxx\RelWithDebInfo\4058424q\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2