import 'package:flutter/material.dart';
import '../services/c72_native_scanner_service.dart';
import '../services/scanner_service.dart';

/// Écran de test pour le scanner C72 natif
class C72NativeScannerScreen extends StatefulWidget {
  final void Function(String, {Map<String, dynamic>? metadata}) onScan;

  const C72NativeScannerScreen({super.key, required this.onScan});

  @override
  State<C72NativeScannerScreen> createState() => _C72NativeScannerScreenState();
}

class _C72NativeScannerScreenState extends State<C72NativeScannerScreen> {
  C72NativeScannerService? _c72Scanner;
  bool _isLoading = true;
  bool _isScanning = false;
  bool _isAvailable = false;
  String? _errorMessage;
  String? _lastScanResult;
  Map<String, dynamic>? _deviceInfo;

  @override
  void initState() {
    super.initState();
    _initializeC72();
  }

  @override
  void dispose() {
    _c72Scanner?.dispose();
    super.dispose();
  }

  /// Initialise le scanner C72 natif
  Future<void> _initializeC72() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      _c72Scanner = C72NativeScannerService();
      
      // Vérifier la disponibilité
      final isAvailable = await _c72Scanner!.isAvailable();
      
      if (!isAvailable) {
        throw Exception('Scanner C72 natif non disponible sur cet appareil');
      }

      // Initialiser le scanner
      final initialized = await _c72Scanner!.initialize();
      
      if (!initialized) {
        throw Exception('Échec de l\'initialisation du scanner C72');
      }

      // Obtenir les informations de l'appareil
      _deviceInfo = await _c72Scanner!.getDeviceInfo();

      // Écouter les résultats de scan
      _c72Scanner!.scanResults.listen(
        (scanResult) {
          _handleScanResult(scanResult);
        },
        onError: (error) {
          _handleScanError(error);
        },
      );

      setState(() {
        _isLoading = false;
        _isAvailable = true;
      });

    } catch (e) {
      setState(() {
        _isLoading = false;
        _isAvailable = false;
        _errorMessage = 'Erreur d\'initialisation: $e';
      });
    }
  }

  /// Démarre le scan
  Future<void> _startScan() async {
    if (_c72Scanner == null || _isScanning || !_isAvailable) return;

    try {
      setState(() {
        _isScanning = true;
        _errorMessage = null;
      });

      await _c72Scanner!.startScan();
    } catch (e) {
      setState(() {
        _isScanning = false;
        _errorMessage = 'Erreur de démarrage: $e';
      });
    }
  }

  /// Arrête le scan
  Future<void> _stopScan() async {
    if (_c72Scanner == null || !_isScanning) return;

    try {
      await _c72Scanner!.stopScan();
      setState(() {
        _isScanning = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur d\'arrêt: $e';
      });
    }
  }

  /// Gère les résultats de scan
  void _handleScanResult(ScanResult scanResult) {
    print('Scan C72 natif reçu: ${scanResult.data}');
    
    setState(() {
      _lastScanResult = scanResult.data;
      _isScanning = false;
    });
    
    // Appeler le callback
    widget.onScan(scanResult.data, metadata: scanResult.metadata);
    
    // Fermer l'écran après un court délai
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        Navigator.pop(context);
      }
    });
  }

  /// Gère les erreurs de scan
  void _handleScanError(dynamic error) {
    setState(() {
      _errorMessage = 'Erreur de scan: $error';
      _isScanning = false;
    });
  }

  /// Teste le scanner
  Future<void> _testScanner() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final testResult = await _c72Scanner!.testScanner();
      
      setState(() {
        _isLoading = false;
        _errorMessage = testResult 
          ? 'Test réussi ✅' 
          : 'Test échoué ❌';
      });

    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Erreur de test: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Scanner C72 Natif"),
        backgroundColor: Colors.blue.shade800,
        foregroundColor: Colors.white,
        actions: [
          if (_isAvailable)
            Icon(
              Icons.check_circle,
              color: Colors.green.shade300,
            ),
          if (!_isAvailable && !_isLoading)
            Icon(
              Icons.error,
              color: Colors.red.shade300,
            ),
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: _showDeviceInfo,
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// Construit le corps de l'écran selon l'état
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Initialisation du scanner C72 natif...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _initializeC72,
                  child: const Text('Réessayer'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _testScanner,
                  child: const Text('Tester'),
                ),
              ],
            ),
          ],
        ),
      );
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icône du scanner C72
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: _isScanning ? Colors.green.shade100 : Colors.blue.shade100,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.qr_code_scanner,
              size: 80,
              color: _isScanning ? Colors.green.shade600 : Colors.blue.shade600,
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Statut
          Text(
            _isScanning 
              ? 'Scanner C72 Natif Actif\nAppuyez sur le bouton de scan du C72'
              : _isAvailable
                ? 'Scanner C72 Natif Prêt\nAppuyez sur le bouton pour démarrer'
                : 'Scanner C72 Natif Non Connecté',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 18,
              color: _isAvailable ? Colors.black87 : Colors.red.shade600,
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Indicateur de scan en cours
          if (_isScanning)
            Column(
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  'En attente du scan...',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.green.shade600,
                  ),
                ),
              ],
            ),
          
          // Dernier résultat
          if (_lastScanResult != null)
            Container(
              margin: const EdgeInsets.only(top: 24),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                border: Border.all(color: Colors.green.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  const Text(
                    'Dernier scan:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _lastScanResult!,
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// Bouton d'action flottant
  Widget? _buildFloatingActionButton() {
    if (!_isAvailable) return null;

    return FloatingActionButton.extended(
      onPressed: _isScanning ? _stopScan : _startScan,
      icon: Icon(_isScanning ? Icons.stop : Icons.play_arrow),
      label: Text(_isScanning ? 'Arrêter' : 'Scanner'),
      backgroundColor: _isScanning ? Colors.red : Colors.green,
    );
  }

  /// Affiche les informations de l'appareil
  void _showDeviceInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Informations C72'),
        content: _deviceInfo != null
          ? Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: _deviceInfo!.entries.map((entry) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Text('${entry.key}: ${entry.value}'),
                );
              }).toList(),
            )
          : const Text('Informations non disponibles'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }
}
