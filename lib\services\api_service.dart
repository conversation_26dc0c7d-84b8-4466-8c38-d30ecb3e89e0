import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiService {
  static const String baseUrl = 'http://localhost:3000/api';

  /// Envoie les données du QR code scanné vers le serveur
  static Future<ApiResponse> sendQrData(
    String qrData, {
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final url = Uri.parse('$baseUrl/qr-scan');

      // Préparer les données à envoyer
      final Map<String, dynamic> requestBody = {
        'qr_data': qrData,
        'timestamp': DateTime.now().toIso8601String(),
        'device_info': 'Flutter App C72',
        if (metadata != null) ...metadata,
      };

      // Effectuer la requête POST
      final response = await http
          .post(
            url,
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: json.encode(requestBody),
          )
          .timeout(const Duration(seconds: 10));

      // Traiter la réponse
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final responseData = json.decode(response.body);
        return ApiResponse(
          success: true,
          message: responseData['message'] ?? 'Données envoyées avec succès',
          data: responseData,
        );
      } else {
        return ApiResponse(
          success: false,
          message: 'Erreur serveur: ${response.statusCode}',
          data: null,
        );
      }
    } catch (e) {
      return ApiResponse(
        success: false,
        message: 'Erreur de connexion: $e',
        data: null,
      );
    }
  }
}

/// Classe pour encapsuler la réponse de l'API
class ApiResponse {
  final bool success;
  final String message;
  final dynamic data;

  ApiResponse({required this.success, required this.message, this.data});

  @override
  String toString() {
    return 'ApiResponse(success: $success, message: $message, data: $data)';
  }
}
