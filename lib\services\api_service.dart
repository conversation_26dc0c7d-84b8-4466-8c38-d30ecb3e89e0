import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';

class ApiService {
  /// Envoie les données du QR code scanné vers le serveur
  static Future<ApiResponse> sendQrData(String qrData) async {
    try {
      final url = Uri.parse('${ApiConfig.apiUrl}${ApiConfig.qrScanEndpoint}');

      // Préparer les données à envoyer
      final Map<String, dynamic> requestBody = {
        'qr_data': qrData,
        'timestamp': DateTime.now().toIso8601String(),
        'device_info':
            'Flutter App', // Vous pouvez ajouter plus d'infos sur l'appareil
      };

      // Effectuer la requête POST
      final response = await http
          .post(
            url,
            headers: {
              ...ApiConfig.defaultHeaders,
              // Ajoutez ici vos headers d'authentification si nécessaire
              // 'Authorization': 'Bearer your-token',
            },
            body: json.encode(requestBody),
          )
          .timeout(ApiConfig.connectionTimeout);

      // Traiter la réponse
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final responseData = json.decode(response.body);
        return ApiResponse(
          success: true,
          message: responseData['message'] ?? 'Données envoyées avec succès',
          data: responseData,
        );
      } else {
        return ApiResponse(
          success: false,
          message: 'Erreur serveur: ${response.statusCode}',
          data: null,
        );
      }
    } catch (e) {
      return ApiResponse(
        success: false,
        message: 'Erreur de connexion: $e',
        data: null,
      );
    }
  }

  /// Vérifie la connectivité avec le serveur
  static Future<bool> checkServerConnection() async {
    try {
      final url = Uri.parse('${ApiConfig.apiUrl}${ApiConfig.healthEndpoint}');
      final response = await http.get(url).timeout(ApiConfig.connectionTimeout);
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}

/// Classe pour encapsuler la réponse de l'API
class ApiResponse {
  final bool success;
  final String message;
  final dynamic data;

  ApiResponse({required this.success, required this.message, this.data});

  @override
  String toString() {
    return 'ApiResponse(success: $success, message: $message, data: $data)';
  }
}
