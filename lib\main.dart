import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'services/api_service.dart';
import 'screens/simple_scanner_screen.dart';
import 'screens/network_settings_screen.dart';

void main() {
  runApp(const QRAccessApp());
}

class QRAccessApp extends StatelessWidget {
  const QRAccessApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'QR Access App',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final TextEditingController nomController = TextEditingController();
  final TextEditingController idController = TextEditingController();
  final TextEditingController fonctionController = TextEditingController();

  String? qrData;
  String? scanResult;
  String? apiStatus;
  bool isLoading = false;

  /// Envoie les données du QR scanné vers l'API
  Future<void> _sendQrDataToApi(
    String qrData, {
    Map<String, dynamic>? metadata,
  }) async {
    setState(() {
      isLoading = true;
      apiStatus = "Envoi en cours...";
    });

    try {
      final response = await ApiService.sendQrData(qrData, metadata: metadata);

      setState(() {
        isLoading = false;
        if (response.success) {
          apiStatus = "✅ ${response.message}";
        } else {
          apiStatus = "❌ ${response.message}";
        }
      });

      // Afficher un snackbar pour informer l'utilisateur
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.message),
            backgroundColor: response.success ? Colors.green : Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      setState(() {
        isLoading = false;
        apiStatus = "❌ Erreur: $e";
      });
    }
  }

  /// Ouvre l'écran de configuration réseau
  Future<void> _openNetworkSettings() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const NetworkSettingsScreen()),
    );

    // Si les paramètres ont été modifiés, on peut rafraîchir l'état
    if (result == true) {
      setState(() {
        apiStatus = "Configuration réseau mise à jour";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("QR Access Generator"),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _openNetworkSettings(),
            tooltip: "Configuration réseau",
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView(
          children: [
            TextField(
              controller: nomController,
              decoration: const InputDecoration(labelText: "Nom"),
            ),
            TextField(
              controller: idController,
              decoration: const InputDecoration(labelText: "Matricule"),
            ),
            TextField(
              controller: fonctionController,
              decoration: const InputDecoration(labelText: "Fonction"),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                final userData = {
                  "id": idController.text,
                  "nom": nomController.text,
                  "fonction": fonctionController.text,
                };
                setState(() {
                  qrData = userData.toString();
                });
              },
              child: const Text("Générer le QR Code"),
            ),
            const SizedBox(height: 20),
            if (qrData != null)
              QrImageView(data: qrData!, version: QrVersions.auto, size: 200.0),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed:
                  isLoading
                      ? null
                      : () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (_) => SimpleScannerScreen(
                                  onScan: (code, {metadata}) {
                                    setState(() {
                                      scanResult = code;
                                    });
                                    // Envoyer automatiquement les données vers l'API
                                    _sendQrDataToApi(code, metadata: metadata);
                                  },
                                ),
                          ),
                        );
                      },
              child:
                  isLoading
                      ? const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text("Traitement..."),
                        ],
                      )
                      : const Text("Scanner un QR Code"),
            ),
            const SizedBox(height: 10),
            if (scanResult != null)
              Text(
                "QR Scanné :\n$scanResult",
                style: const TextStyle(fontSize: 16),
              ),
            const SizedBox(height: 10),
            if (apiStatus != null)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color:
                      apiStatus!.startsWith('✅')
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.red.withValues(alpha: 0.1),
                  border: Border.all(
                    color:
                        apiStatus!.startsWith('✅') ? Colors.green : Colors.red,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  "Statut API: $apiStatus",
                  style: TextStyle(
                    fontSize: 14,
                    color:
                        apiStatus!.startsWith('✅')
                            ? Colors.green.shade700
                            : Colors.red.shade700,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
