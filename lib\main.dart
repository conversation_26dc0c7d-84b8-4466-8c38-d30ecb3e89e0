import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

void main() {
  runApp(const QRAccessApp());
}

class QRAccessApp extends StatelessWidget {
  const QRAccessApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'QR Access App',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final TextEditingController nomController = TextEditingController();
  final TextEditingController idController = TextEditingController();
  final TextEditingController fonctionController = TextEditingController();

  String? qrData;
  String? scanResult;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("QR Access Generator")),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView(
          children: [
            TextField(
              controller: nomController,
              decoration: const InputDecoration(labelText: "Nom"),
            ),
            TextField(
              controller: idController,
              decoration: const InputDecoration(labelText: "Matricule"),
            ),
            TextField(
              controller: fonctionController,
              decoration: const InputDecoration(labelText: "Fonction"),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                final userData = {
                  "id": idController.text,
                  "nom": nomController.text,
                  "fonction": fonctionController.text,
                };
                setState(() {
                  qrData = userData.toString();
                });
              },
              child: const Text("Générer le QR Code"),
            ),
            const SizedBox(height: 20),
            if (qrData != null)
              QrImageView(data: qrData!, version: QrVersions.auto, size: 200.0),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (_) => ScannerScreen(
                          onScan: (code) {
                            setState(() {
                              scanResult = code;
                            });
                          },
                        ),
                  ),
                );
              },
              child: const Text("Scanner un QR Code"),
            ),
            const SizedBox(height: 10),
            if (scanResult != null)
              Text(
                "QR Scanné :\n$scanResult",
                style: const TextStyle(fontSize: 16),
              ),
          ],
        ),
      ),
    );
  }
}

class ScannerScreen extends StatelessWidget {
  final void Function(String) onScan;

  const ScannerScreen({super.key, required this.onScan});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Scanner")),
      body: MobileScanner(
        onDetect: (capture) {
          final code = capture.barcodes.first.rawValue;
          if (code != null) {
            onScan(code);
            Navigator.pop(context);
          }
        },
      ),
    );
  }
}
