{"buildFiles": ["D:\\flutter_windows_3.29.3-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\test_qr\\android\\app\\.cxx\\Debug\\1r615z6z\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\test_qr\\android\\app\\.cxx\\Debug\\1r615z6z\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}