//
//  Generated file. Do not edit.
//

// clang-format off

#import "GeneratedPluginRegistrant.h"

#if __has_include(<mobile_scanner/MobileScannerPlugin.h>)
#import <mobile_scanner/MobileScannerPlugin.h>
#else
@import mobile_scanner;
#endif

#if __has_include(<rfid_c72_plugin/RfidC72Plugin.h>)
#import <rfid_c72_plugin/RfidC72Plugin.h>
#else
@import rfid_c72_plugin;
#endif

@implementation GeneratedPluginRegistrant

+ (void)registerWithRegistry:(NSObject<FlutterPluginRegistry>*)registry {
  [MobileScannerPlugin registerWithRegistrar:[registry registrarForPlugin:@"MobileScannerPlugin"]];
  [RfidC72Plugin registerWithRegistrar:[registry registrarForPlugin:@"RfidC72Plugin"]];
}

@end
