import 'dart:async';
import 'package:flutter/services.dart';
import 'scanner_service.dart';

/// Service pour le scanner C72 utilisant notre plugin natif
class C72NativeScannerService extends ScannerService {
  static const MethodChannel _channel = MethodChannel('c72_barcode_scanner');
  static const EventChannel _eventChannel = EventChannel('c72_barcode_scanner/events');
  
  final StreamController<ScanResult> _scanController = StreamController<ScanResult>.broadcast();
  StreamSubscription? _eventSubscription;
  bool _isInitialized = false;
  bool _isScanning = false;

  @override
  ScannerType get scannerType => ScannerType.c72Device;

  @override
  Stream<ScanResult> get scanResults => _scanController.stream;

  @override
  bool get isScanning => _isScanning;

  @override
  Future<bool> isAvailable() async {
    try {
      final bool available = await _channel.invokeMethod('isAvailable');
      return available;
    } catch (e) {
      print('Erreur lors de la vérification de disponibilité C72: $e');
      return false;
    }
  }

  /// Initialise le scanner C72
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Initialiser le plugin natif
      final bool success = await _channel.invokeMethod('initialize');
      
      if (!success) {
        throw Exception('Échec de l\'initialisation du scanner C72');
      }

      // Écouter les événements de scan
      _eventSubscription = _eventChannel.receiveBroadcastStream().listen(
        _handleScanEvent,
        onError: _handleScanError,
      );

      _isInitialized = true;
      print('Scanner C72 natif initialisé avec succès');
      return true;

    } catch (e) {
      print('Erreur lors de l\'initialisation du scanner C72: $e');
      return false;
    }
  }

  @override
  Future<void> startScan() async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) {
        throw Exception('Impossible d\'initialiser le scanner C72');
      }
    }

    if (_isScanning) return;

    try {
      final bool success = await _channel.invokeMethod('startScan');
      
      if (!success) {
        throw Exception('Échec du démarrage du scan');
      }

      _isScanning = true;
      print('Scan C72 natif démarré');

    } catch (e) {
      print('Erreur lors du démarrage du scan C72: $e');
      throw Exception('Impossible de démarrer le scan C72: $e');
    }
  }

  @override
  Future<void> stopScan() async {
    if (!_isScanning) return;

    try {
      final bool success = await _channel.invokeMethod('stopScan');
      
      if (success) {
        _isScanning = false;
        print('Scan C72 natif arrêté');
      }

    } catch (e) {
      print('Erreur lors de l\'arrêt du scan C72: $e');
    }
  }

  @override
  Future<void> dispose() async {
    await stopScan();
    
    // Annuler l'abonnement aux événements
    await _eventSubscription?.cancel();
    _eventSubscription = null;

    // Fermer le scanner natif
    try {
      await _channel.invokeMethod('close');
    } catch (e) {
      print('Erreur lors de la fermeture du scanner C72: $e');
    }

    _isInitialized = false;
    await _scanController.close();
  }

  /// Gère les événements de scan depuis le plugin natif
  void _handleScanEvent(dynamic event) {
    try {
      if (event is Map<String, dynamic>) {
        final bool success = event['success'] ?? false;
        
        if (success) {
          final String data = event['data'] ?? '';
          final int timestamp = event['timestamp'] ?? DateTime.now().millisecondsSinceEpoch;
          final String scannerType = event['scanner_type'] ?? 'c72_integrated';
          
          if (data.isNotEmpty) {
            final scanResult = ScanResult(
              data: data,
              timestamp: DateTime.fromMillisecondsSinceEpoch(timestamp),
              scannerType: this.scannerType,
              metadata: {
                'source': 'c72_native_scanner',
                'device_model': 'Chainway C72',
                'scan_method': 'hardware_integrated',
                'scanner_type': scannerType,
                'sdk_version': 'DeviceAPI_ver20230228',
              },
            );

            _scanController.add(scanResult);
            
            // Le scan s'arrête automatiquement après un scan réussi
            _isScanning = false;
            
            print('Code scanné par C72 natif: $data');
          }
        } else {
          final String error = event['error'] ?? 'Erreur de scan inconnue';
          final int errorCode = event['error_code'] ?? -1;
          
          print('Erreur de scan C72: $error (code: $errorCode)');
          _handleScanError('Erreur de scan: $error');
        }
      }
    } catch (e) {
      print('Erreur lors du traitement de l\'événement de scan: $e');
      _handleScanError('Erreur de traitement: $e');
    }
  }

  /// Gère les erreurs de scan
  void _handleScanError(dynamic error) {
    print('Erreur de scan C72: $error');
    _isScanning = false;
    
    // Optionnel: émettre un événement d'erreur
    // _scanController.addError(error);
  }

  /// Obtient les informations sur l'appareil C72
  Future<Map<String, dynamic>> getDeviceInfo() async {
    try {
      final Map<dynamic, dynamic> info = await _channel.invokeMethod('getDeviceInfo');
      return Map<String, dynamic>.from(info);
    } catch (e) {
      print('Erreur lors de la récupération des infos de l\'appareil: $e');
      return {
        'model': 'Chainway C72',
        'scanner_type': 'Integrated 2D Barcode Scanner',
        'sdk_version': 'DeviceAPI_ver20230228',
        'is_available': false,
        'is_initialized': _isInitialized,
        'is_scanning': _isScanning,
        'error': e.toString(),
      };
    }
  }

  /// Teste la connectivité du scanner
  Future<bool> testScanner() async {
    try {
      final bool available = await isAvailable();
      if (!available) return false;

      final bool initialized = await initialize();
      if (!initialized) return false;

      final Map<String, dynamic> deviceInfo = await getDeviceInfo();
      print('Informations du device C72: $deviceInfo');

      return true;
    } catch (e) {
      print('Erreur lors du test du scanner C72: $e');
      return false;
    }
  }

  /// Configure les paramètres du scanner (si supporté par le SDK)
  Future<void> configureScanner({
    bool enableBeep = true,
    bool enableVibration = true,
    int scanTimeout = 5000,
  }) async {
    try {
      // Note: Ces paramètres dépendent de l'API exacte du SDK C72
      // Pour l'instant, on log juste la configuration
      print('Configuration du scanner C72: beep=$enableBeep, vibration=$enableVibration, timeout=$scanTimeout');
      
      // TODO: Implémenter la configuration si le SDK le supporte
      // await _channel.invokeMethod('configure', {
      //   'enableBeep': enableBeep,
      //   'enableVibration': enableVibration,
      //   'scanTimeout': scanTimeout,
      // });
      
    } catch (e) {
      print('Erreur lors de la configuration du scanner C72: $e');
    }
  }
}
