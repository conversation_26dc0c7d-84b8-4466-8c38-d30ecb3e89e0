<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:tools="http://schemas.android.com/tools"
	android:id="@+id/activity_main"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:orientation="vertical">
<PreferenceScreen android:layout_width="wrap_content">
    
    <!-- Enable/Disable Symbology -->
    <com.rscja.scanner.ui.view.MyPreferenceCategory android:title="@string/sybology_configuration"
		 >
	    <!-- PreferenceScreen for Advanced -->
        <PreferenceScreen
			android:title="@string/symbology_enable_disable_configuration"
			android:summary="@string/symbology_enable_disable_configuration_summary">
			<!-- SYM_TRIOPTIC-->
	  	  <PreferenceScreen
			android:title="@string/sym_trioptic_title"
			android:summary="@string/symbology_select_to_configure_advanced_properties">
			<CheckBoxPreference
				android:title="@string/sym_trioptic_title"
				android:summaryOn="@string/sym_uncheck_to_disable"
				android:summaryOff="@string/sym_check_to_enable"
				android:key="sym_trioptic_enable"
				android:defaultValue="false" />
			</PreferenceScreen>
			<!-- SYM_TLCODE39 -->
	 	<PreferenceScreen
			android:title="@string/sym_tlcode39_title"
			android:summary="@string/symbology_select_to_configure_advanced_properties">
			  <CheckBoxPreference
				android:title="@string/sym_tlcode39_title"
				android:summaryOn="@string/sym_uncheck_to_disable"
				android:summaryOff="@string/sym_check_to_enable"
				android:key="sym_tlcode39_enable"
				android:defaultValue="false" />
	 	</PreferenceScreen>
			<!-- SYM_ISBT -->
		<PreferenceScreen
			android:title="@string/sym_isbt_title"
			android:summary="@string/symbology_select_to_configure_advanced_properties">
			<CheckBoxPreference
				android:title="@string/sym_isbt_title"
				android:summaryOn="@string/sym_uncheck_to_disable"
				android:summaryOff="@string/sym_check_to_enable"
				android:key="sym_isbt_enable"
				android:defaultValue="false" />
			</PreferenceScreen>
			<!-- SYM_COUPONCODE -->
		<PreferenceScreen
			android:title="@string/sym_couponcode_title"
			android:summary="@string/symbology_select_to_configure_advanced_properties">
			<CheckBoxPreference
				android:title="@string/sym_couponcode_title"
				android:summaryOn="@string/sym_uncheck_to_disable"
				android:summaryOff="@string/sym_check_to_enable"
				android:key="sym_couponcode_enable"
				android:defaultValue="false" />
			</PreferenceScreen>
			<!-- SYM_CODE32-->
		<PreferenceScreen
			android:title="@string/sym_code32_title"
			android:summary="@string/symbology_select_to_configure_advanced_properties">
			<CheckBoxPreference
				android:title="@string/sym_code32_title"
				android:summaryOn="@string/sym_uncheck_to_disable"
				android:summaryOff="@string/sym_check_to_enable"
				android:key="sym_code32_enable"
				android:defaultValue="false" />
			</PreferenceScreen>
            <!-- SYM_AZTEC -->

            <PreferenceScreen
	            android:title="@string/sym_aztec_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- Enable/Disable -->
				<!-- For now remove enable/disable from advanced - does not work well with enable/disable prefs -->
		        <!--<CheckBoxPreference-->
		            <!--android:title="@string/sym_enable"-->
		            <!--android:summaryOn="@string/sym_uncheck_to_disable"-->
		            <!--android:summaryOff="@string/sym_check_to_enable"-->
		            <!--android:key="sym_aztec_advanced_enable"-->
		            <!--android:defaultValue="false" />-->
				<CheckBoxPreference
					android:title="@string/sym_aztec_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_aztec_enable"
					android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_aztec_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_aztec_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_CODABAR -->
            <PreferenceScreen 
	            android:title="@string/sym_codabar_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<CheckBoxPreference
					android:title="@string/sym_codabar_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_codabar_enable"
					android:defaultValue="false" />
		        <!-- Check enable -->
		        <CheckBoxPreference
		            android:title="@string/sym_check_enable_title"
		            android:key="sym_codabar_check_enable"
		            android:defaultValue="false" />
		        <!-- Start/Stop Transmit -->
		        <CheckBoxPreference
		            android:title="@string/sym_start_stop_transmit_enable_title"
		            android:key="sym_codabar_start_stop_transmit_enable"
		            android:defaultValue="false" />
		        <!-- Codabar concatenate -->
		        <CheckBoxPreference
		            android:title="@string/sym_codabar_concatenate_enable_title"
		            android:key="sym_codabar_concatenate_enable"
		            android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_codabar_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_codabar_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_CODABLOCK -->
            <PreferenceScreen 
	            android:title="@string/sym_codablock_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<CheckBoxPreference
					android:title="@string/sym_codablock_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_codablock_enable"
					android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_codablock_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_codablock_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
            
            <!-- SYM_CODE11 -->
            <PreferenceScreen 
	            android:title="@string/sym_code11_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- Enable/Disable -->
				<!-- For now remove enable/disable from advanced - does not work well with enable/disable prefs
		        <CheckBoxPreference
		            android:title="@string/sym_enable"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="symQrEnableFlag"
		            android:defaultValue="false" />
		        -->
				<!-- SYM_CODE11-->
				<CheckBoxPreference
					android:title="@string/sym_code11_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_code11_enable"
					android:defaultValue="false" />
		        <!-- Check enable -->
		        <CheckBoxPreference
		            android:title="@string/sym_check_enable_title"
		            android:key="sym_code11_check_enable"
		            android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_code11_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_code11_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_CODE39 -->
            <PreferenceScreen 
	            android:title="@string/sym_code39_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">

				<!-- SYM_CODE39 -->
				<CheckBoxPreference
					android:title="@string/sym_code39_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_code39_enable"
					android:defaultValue="false" />
		        <!-- Check enable -->
		        <CheckBoxPreference
		            android:title="@string/sym_check_enable_title"
		            android:key="sym_code39_check_enable"
		            android:defaultValue="false" />
		        <!-- Start/Stop Transmit -->
		        <CheckBoxPreference
		            android:title="@string/sym_start_stop_transmit_enable_title"
					android:summary="start and stop transmit"
		            android:key="sym_code39_start_stop_transmit_enable"
		            android:defaultValue="false" />
		        <!-- Append -->
		        <CheckBoxPreference
		            android:title="@string/sym_append_enable_title"
		            android:key="sym_code39_append_enable"
		            android:defaultValue="false" />
		        <!-- Full Ascii -->
		        <CheckBoxPreference
		            android:title="@string/sym_fullascii_enable_title"
					android:summary="fullascii"
		            android:key="sym_code39_fullascii_enable"
		            android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_code39_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_code39_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_CODE93 -->
            <PreferenceScreen 
	            android:title="@string/sym_code93_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">

				<!-- SYM_CODE93 -->
				<CheckBoxPreference
					android:title="@string/sym_code93_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_code93_enable"
					android:defaultValue="false" />

		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_code93_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_code93_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
           <!-- SYM_CODE128 -->
            <PreferenceScreen 
	            android:title="@string/sym_code128_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_CODE128-->
				<CheckBoxPreference
					android:title="@string/sym_code128_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_code128_enable"
					android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_code128_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_code128_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_COMPOSITE -->
            <PreferenceScreen 
	            android:title="@string/sym_composite_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_COMPOSITE -->
				<CheckBoxPreference
					android:title="@string/sym_composite_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_composite_enable"
					android:defaultValue="false" />
	            <!-- Composite UPC -->
		        <CheckBoxPreference
		            android:title="@string/sym_composite_upc_enable_title"
		            android:key="sym_composite_upc_enable"
		            android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_composite_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_composite_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_CHINAPOST -->
            <PreferenceScreen 
	            android:title="@string/sym_chinapost_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_chinapost_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_chinapost_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
            
            <!-- SYM_DATAMATRIX -->
            <PreferenceScreen 
	            android:title="@string/sym_datamatrix_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- Enable/Disable -->
		        <!-- For now remove enable/disable from advanced - does not work well with enable/disable prefs
		        <CheckBoxPreference
		            android:title="@string/sym_enable"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="symDataMatrixEnableFlag"
		            android:defaultValue="false" />
		        -->
				<!-- SYM_DATAMATRIX -->
				<CheckBoxPreference
					android:title="@string/sym_datamatrix_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_datamatrix_enable"
					android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_datamatrix_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_datamatrix_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_EAN8 -->
            <PreferenceScreen 
	            android:title="@string/sym_ean8_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">

				<!-- SYM_EAN8 -->
				<CheckBoxPreference
					android:title="@string/sym_ean8_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_ean8_enable"
					android:defaultValue="false" />
				<!-- Check transmit -->
		        <CheckBoxPreference
		            android:title="@string/sym_check_transmit_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_ean8_check_transmit_enable"
		            android:defaultValue="false" />
		        <!-- Addenda separator -->
		        <CheckBoxPreference
		            android:title="@string/sym_addenda_separator_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_ean8_addenda_separator_enable"
		            android:defaultValue="false" />
		        <!-- 2 digit addenda -->
		        <CheckBoxPreference
		            android:title="@string/sym_2_digit_addenda_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_ean8_2_digit_addenda_enable"
		            android:defaultValue="false" />
		        <!-- 5 digit addenda -->
		        <CheckBoxPreference
		            android:title="@string/sym_5_digit_addenda_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_ean8_5_digit_addenda_enable"
		            android:defaultValue="false" />
		        <!-- Addenda required -->
		        <CheckBoxPreference
		            android:title="@string/sym_addenda_required_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_ean8_addenda_required_enable"
		            android:defaultValue="false" />
		    </PreferenceScreen>
   
            <!-- SYM_EAN13 -->
            <PreferenceScreen 
	            android:title="@string/sym_ean13_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_EAN13 -->
				<CheckBoxPreference
					android:title="@string/sym_ean13_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_ean13_enable"
					android:defaultValue="false" />
				<!-- Check transmit -->
		        <CheckBoxPreference
		            android:title="@string/sym_check_transmit_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_ean13_check_transmit_enable"
		            android:defaultValue="false" />
		        <!-- Addenda separator -->
		        <CheckBoxPreference
		            android:title="@string/sym_addenda_separator_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_ean13_addenda_separator_enable"
		            android:defaultValue="false" />
		        <!-- 2 digit addenda -->
		        <CheckBoxPreference
		            android:title="@string/sym_2_digit_addenda_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_ean13_2_digit_addenda_enable"
		            android:defaultValue="false" />
		        <!-- 5 digit addenda -->
		        <CheckBoxPreference
		            android:title="@string/sym_5_digit_addenda_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_ean13_5_digit_addenda_enable"
		            android:defaultValue="false" />
		        <!-- Addenda required -->
		        <CheckBoxPreference
		            android:title="@string/sym_addenda_required_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_ean13_addenda_required_enable"
		            android:defaultValue="false" />
		    </PreferenceScreen>
  
            <!-- SYM_GS1_128 -->
            <PreferenceScreen 
	            android:title="@string/sym_gs1_128_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_GS1_128-->
				<CheckBoxPreference
					android:title="@string/sym_gs1_128_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_gs1_128_enable"
					android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_gs1_128_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_gs1_128_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
            
         	<!-- SYM_HANXIN -->
            <PreferenceScreen 
	            android:title="@string/sym_hanxin_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_HANXIN-->
				<CheckBoxPreference
					android:title="@string/sym_hanxin_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_hanxin_enable"
					android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_hanxin_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_hanxin_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_IATA25-->
            <PreferenceScreen 
	            android:title="@string/sym_iata25_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_IATA25 -->
				<CheckBoxPreference
					android:title="@string/sym_iata25_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_iata25_enable"
					android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_iata25_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_iata25_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_INT25 -->
            <PreferenceScreen 
	            android:title="@string/sym_int25_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_INT25 -->
				<CheckBoxPreference
					android:title="@string/sym_int25_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_int25_enable"
					android:defaultValue="false" />
	            <!-- Check enable -->
		        <CheckBoxPreference
		            android:title="@string/sym_check_enable_title"
		            android:key="sym_int25_check_enable"
		            android:defaultValue="false" />
		        <!-- Check Transmit Enable -->
		        <CheckBoxPreference
		            android:title="@string/sym_check_transmit_enable_title"
		            android:key="sym_int25_check_transmit_enable"
		            android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_int25_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_int25_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_KOREAPOST -->
            <PreferenceScreen 
	            android:title="@string/sym_koreapost_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_koreapost_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_koreapost_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_MATRIX25 -->
            <PreferenceScreen 
	            android:title="@string/sym_matrix25_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_MATRIX25-->
				<CheckBoxPreference
					android:title="@string/sym_matrix25_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_matrix25_enable"
					android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_matrix25_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_matrix25_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_MAXICODE -->
            <PreferenceScreen 
	            android:title="@string/sym_maxicode_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_MAXICODE-->
				<CheckBoxPreference
					android:title="@string/sym_maxicode_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_maxicode_enable"
					android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_maxicode_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_maxicode_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_MICROPDF -->
            <PreferenceScreen 
	            android:title="@string/sym_micropdf_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_MICROPDF -->
				<CheckBoxPreference
					android:title="@string/sym_micropdf_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_micropdf_enable"
					android:defaultValue="false" />

				<!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_micropdf_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_micropdf_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_MSI -->
            <PreferenceScreen 
	            android:title="@string/sym_msi_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_MSI -->
				<CheckBoxPreference
					android:title="@string/sym_msi_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_msi_enable"
					android:defaultValue="false" />
		        <!-- Check enable -->
		        <CheckBoxPreference
		            android:title="@string/sym_check_transmit_enable_title"
		            android:key="sym_msi_check_transmit_enable"
		            android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_msi_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_msi_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_PDF417 -->
            <PreferenceScreen 
	            android:title="@string/sym_pdf417_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_PDF417 -->
				<CheckBoxPreference
					android:title="@string/sym_pdf417_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_pdf417_enable"
					android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_pdf417_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_pdf417_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
            
            <!-- SYM_POSTNET -->
            <PreferenceScreen 
	            android:title="@string/sym_postnet_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
		        <!-- Check enable -->
		        <CheckBoxPreference
		            android:title="@string/sym_check_transmit_enable_title"
		            android:key="sym_postnet_check_transmit_enable"
		            android:defaultValue="false" />
		    </PreferenceScreen>
            
            <!-- SYM_QR -->
            <PreferenceScreen 
	            android:title="@string/sym_qr_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_QR-->
				<CheckBoxPreference
					android:title="@string/sym_qr_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_qr_enable"
					android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_qr_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_qr_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_RSS -->
            <PreferenceScreen 
	            android:title="@string/sym_rss_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_RSS -->
				<CheckBoxPreference
					android:title="@string/sym_rss_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_rss_rss_enable"
					android:defaultValue="false" />
	            <!-- RSL Enable -->
		        <CheckBoxPreference
		            android:title="@string/sym_rss_rsl_enable_title"
		            android:key="sym_rss_rsl_enable"
		            android:defaultValue="false" />
		        <!-- RSE Enable -->
		        <CheckBoxPreference
		            android:title="@string/sym_rss_rse_enable_title"
		            android:key="sym_rss_rse_enable"
		            android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_rss_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_rss_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
		    <!-- SYM_STRT25 -->
            <PreferenceScreen 
	            android:title="@string/sym_strt25_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_STRT25 -->
				<CheckBoxPreference
					android:title="@string/sym_strt25_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_strt25_enable"
					android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_strt25_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_strt25_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_TELEPEN -->
            <PreferenceScreen 
	            android:title="@string/sym_telepen_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">
				<!-- SYM_TELEPEN -->
				<CheckBoxPreference
					android:title="@string/sym_telepen_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_telepen_enable"
					android:defaultValue="false" />
		        <!-- Old sytle -->
		        <CheckBoxPreference
		            android:title="@string/sym_telepen_old_style_enable_title"
		            android:key="sym_telepen_telepen_old_style"
		            android:defaultValue="false" />
		        <!-- MinLength -->
		        <EditTextPreference 
	                android:title="@string/sym_minlength_title"
	                android:summary="@string/sym_minlength_summary"
	                android:key="sym_telepen_min"
	                android:inputType="number"
	                android:defaultValue="0" />
		        <!-- MaxLength -->
	            <EditTextPreference 
	                android:title="@string/sym_maxlength_title"
	                android:summary="@string/sym_maxlength_summary"
	                android:key="sym_telepen_max"
	                android:inputType="number"
	                android:defaultValue="1" />
		    </PreferenceScreen>
		    
            <!-- SYM_UPCA -->
            <PreferenceScreen 
	            android:title="@string/sym_upca_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">

				<!-- SYM_UPCA -->
				<CheckBoxPreference
					android:title="@string/sym_upca_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_upca_enable"
					android:defaultValue="false" />
				<!-- Check transmit -->
		        <CheckBoxPreference
		            android:title="@string/sym_check_transmit_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_upca_check_transmit_enable"
		            android:defaultValue="false" />
		        <!-- Num transmit -->
		        <CheckBoxPreference
		            android:title="@string/sym_check_transmit_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_upca_sys_num_transmit_enable"
		            android:defaultValue="false" />
		        <!-- Addenda separator -->
		        <CheckBoxPreference
		            android:title="@string/sym_addenda_separator_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_upca_addenda_separator_enable"
		            android:defaultValue="false" />
		        <!-- 2 digit addenda -->
		        <CheckBoxPreference
		            android:title="@string/sym_2_digit_addenda_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_upca_2_digit_addenda_enable"
		            android:defaultValue="false" />
		        <!-- 5 digit addenda -->
		        <CheckBoxPreference
		            android:title="@string/sym_5_digit_addenda_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_upca_5_digit_addenda_enable"
		            android:defaultValue="false" />
		        <!-- Addenda required -->
		        <CheckBoxPreference
		            android:title="@string/sym_addenda_required_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_upca_addenda_required_enable"
		            android:defaultValue="false" />
		    </PreferenceScreen>
		    
            <!-- SYM_UPCE0 -->
            <PreferenceScreen 
	            android:title="@string/sym_upce0_title"
	            android:summary="@string/symbology_select_to_configure_advanced_properties">

				<!-- SYM_UPCE0 -->
				<CheckBoxPreference
					android:title="@string/sym_upce0_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_upce0_enable"
					android:defaultValue="false" />
				<!-- UPCE0 expanded -->
		        <CheckBoxPreference
		            android:title="@string/sym_upce0_expanded_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_upce0_upce0_expanded_enable"
		            android:defaultValue="false" />
				<!-- Check transmit -->
		        <CheckBoxPreference
		            android:title="@string/sym_check_transmit_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_upce0_check_transmit_enable"
		            android:defaultValue="false" />
		        <!-- Num transmit -->
		        <CheckBoxPreference
		            android:title="@string/sym_num_transmit_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_upce0_sys_num_transmit_enable"
		            android:defaultValue="false" />
		        <!-- Addenda separator -->
		        <CheckBoxPreference
		            android:title="@string/sym_addenda_separator_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_upce0_addenda_separator_enable"
		            android:defaultValue="false" />
		        <!-- 2 digit addenda -->
		        <CheckBoxPreference
		            android:title="@string/sym_2_digit_addenda_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_upce0_2_digit_addenda_enable"
		            android:defaultValue="false" />
		        <!-- 5 digit addenda -->
		        <CheckBoxPreference
		            android:title="@string/sym_5_digit_addenda_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_upce0_5_digit_addenda_enable"
		            android:defaultValue="false" />
		        <!-- Addenda required -->
		        <CheckBoxPreference
		            android:title="@string/sym_addenda_required_enable_title"
		            android:summaryOn="@string/sym_uncheck_to_disable"
		            android:summaryOff="@string/sym_check_to_enable"
		            android:key="sym_upce0_addenda_required_enable"
		            android:defaultValue="false" />
		    </PreferenceScreen>
			<!-- SYM_UPCE1 -->
		  <PreferenceScreen
				android:title="@string/sym_upce1_title"
				android:summary="@string/symbology_select_to_configure_advanced_properties">
				<CheckBoxPreference
					android:title="@string/sym_upce1_title"
					android:summaryOn="@string/sym_uncheck_to_disable"
					android:summaryOff="@string/sym_check_to_enable"
					android:key="sym_upce1_upce1_enable"
					android:defaultValue="false" />
			</PreferenceScreen>
    	</PreferenceScreen>

    </com.rscja.scanner.ui.view.MyPreferenceCategory>
    
	<!-- Decoding Preferences -->
   
    <com.rscja.scanner.ui.view.MyPreferenceCategory android:title="Decoding Preferences" >
		<!-- Decode Timeout-->
        <EditTextPreference
            android:title="Decode Timeout"
            android:summary="Sets the decode timeout (seconds)"
            android:key="decoding_pref_decode_timeout"
            android:inputType="number"
            android:defaultValue="10"/>
            <!-- DecodeTimeLimit -->
	            <EditTextPreference 
	                android:title="@string/decode_time_limit_title"
	                android:summary="@string/decode_time_limit_summary"
	                android:key="decode_time_limit"
	                android:inputType="number"
	                android:defaultValue="800" />
    </com.rscja.scanner.ui.view.MyPreferenceCategory>
    
     <!-- Scanning Preferences -->
   
    <com.rscja.scanner.ui.view.MyPreferenceCategory android:title="@string/scanning_preferences" >
            <ListPreference
            android:title="@string/title_lights_config"
            android:summary="@string/title_lights_config_summary"
	        android:defaultValue="3"
	        android:entries="@array/lights_config_values_titles"
	        android:entryValues="@array/lights_config_values"
	        android:key="lightsConfig"
	        android:negativeButtonText="@null"
	        android:positiveButtonText="@null" />
    </com.rscja.scanner.ui.view.MyPreferenceCategory>


	<com.rscja.scanner.ui.view.MyPreferenceCategory android:title="Decoding Preferences" >
		<!-- Decode Windowing -->
		<PreferenceScreen
			android:title="Decode Windowing"
			android:summary="Select to configure decode windowing">
			<!-- Enable/Disable -->
			<CheckBoxPreference
				android:title="Configure Windowing"
				android:summaryOn="Uncheck to disable"
				android:summaryOff="Check to enable"
				android:key="decode_centering_enable"
				android:defaultValue="false" />
			<!-- Windowing Mode -->
			<ListPreference
				android:title="@string/decode_centering_mode_list_title"
				android:summary="@string/decode_centering_mode_list_summary"
				android:defaultValue="2"
				android:entries="@array/decode_centering_mode_entries"
				android:entryValues="@array/decode_centering_mode_values"
				android:key="decode_centering_mode"
				android:dependency="decode_centering_enable"
				android:negativeButtonText="@null"
				android:positiveButtonText="@null" />
			<!-- Upper Left X -->
			<EditTextPreference
				android:title="UpperLeftWindowX"
				android:summary="Sets the upper left X value"
				android:key="decode_window_upper_left_x"
				android:inputType="number"
				android:defaultValue="0"
				android:dependency="decode_centering_enable" />
			<!-- Upper Left Y -->
			<EditTextPreference
				android:title="UpperLeftWindowY"
				android:summary="Sets the upper left Y value"
				android:key="decode_window_upper_left_y"
				android:inputType="number"
				android:defaultValue="0"
				android:dependency="decode_centering_enable"/>
			<!-- Lower Right X -->
			<EditTextPreference
				android:title="LowerRightWindowX"
				android:summary="Sets the lower_right X value"
				android:key="decode_window_lower_right_x"
				android:inputType="number"
				android:defaultValue="0"
				android:dependency="decode_centering_enable"/>
			<!-- Lower Right Y -->
			<EditTextPreference
				android:title="LowerRightWindowY"
				android:summary="Sets the lower_right Y value"
				android:key="decode_window_lower_right_y"
				android:inputType="number"
				android:defaultValue="0"
				android:dependency="decode_centering_enable"/>
			<!-- Debug Window -->
			<CheckBoxPreference
				android:title="Enable Debug Window"
				android:summaryOn="Uncheck to disable"
				android:summaryOff="Check to enable"
				android:key="decode_debug_window_enable"
				android:defaultValue="false" />
		</PreferenceScreen>


	</com.rscja.scanner.ui.view.MyPreferenceCategory>

</PreferenceScreen>
</LinearLayout>