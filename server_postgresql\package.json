{"name": "qr-scanner-postgresql-server", "version": "1.0.0", "description": "Serveur API local pour recevoir les données du QR Scanner C72 et les stocker dans PostgreSQL", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-database.js", "test": "node test-connection.js", "check-data": "node check-data.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "pg": "^8.11.3", "dotenv": "^16.3.1", "helmet": "^7.1.0", "morgan": "^1.10.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["qr", "scanner", "api", "flutter", "c72", "postgresql", "wifi"], "author": "Your Name", "license": "MIT"}