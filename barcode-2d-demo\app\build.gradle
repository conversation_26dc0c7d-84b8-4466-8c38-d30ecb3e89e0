apply plugin: 'com.android.application'

android {
    compileSdkVersion 29
    buildToolsVersion "30.0.1"
    ndkVersion '21.3.6528147'
    defaultConfig {
        applicationId "com.example.barcode2ds"
        minSdkVersion 18
        targetSdkVersion 29
        versionCode 1
        versionName "1.3"
    }

    signingConfigs {
        debug {
            File strFile = new File("/release.jks")
            storeFile file(strFile)
            storePassword "123456"
            keyPassword "123456"
            keyAlias "key0"
        }
        release {
            File strFile = new File("/release.jks")
            storeFile file(strFile)
            storePassword "123456"
            keyPassword "123456"
            keyAlias "key0"
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        // 自定义apk名称
        android.applicationVariants.all { variant ->
            variant.outputs.all {
                outputFileName = "barcode-2d-demo_v${defaultConfig.versionName}.apk"
            }
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
}
