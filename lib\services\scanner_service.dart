import 'dart:async';
import 'c72_scanner_service.dart';
import 'c72_native_scanner_service.dart';

/// Énumération des types de scanners disponibles
enum ScannerType {
  mobileCamera, // Scanner via caméra standard
  c72Device, // Scanner via device C72
}

/// Résultat d'un scan
class ScanResult {
  final String data;
  final DateTime timestamp;
  final ScannerType scannerType;
  final Map<String, dynamic>? metadata;

  ScanResult({
    required this.data,
    required this.timestamp,
    required this.scannerType,
    this.metadata,
  });

  @override
  String toString() {
    return 'ScanResult(data: $data, timestamp: $timestamp, scannerType: $scannerType)';
  }
}

/// Interface abstraite pour tous les types de scanners
abstract class ScannerService {
  /// Type de scanner
  ScannerType get scannerType;

  /// Vérifie si le scanner est disponible sur cet appareil
  Future<bool> isAvailable();

  /// Démarre le scan
  Future<void> startScan();

  /// Arrête le scan
  Future<void> stopScan();

  /// Stream des résultats de scan
  Stream<ScanResult> get scanResults;

  /// Libère les ressources
  Future<void> dispose();

  /// Vérifie si le scan est en cours
  bool get isScanning;
}

/// Factory pour créer le bon type de scanner selon l'appareil
class ScannerFactory {
  static ScannerService? _instance;

  /// Obtient l'instance du scanner approprié pour cet appareil
  static Future<ScannerService> getInstance() async {
    if (_instance != null) {
      return _instance!;
    }

    // Détecter le type d'appareil et créer le scanner approprié
    if (await _isC72Device()) {
      print('Device C72 détecté, utilisation du scanner natif intégré');
      _instance = C72NativeScannerService();
    } else {
      print('Device standard détecté, utilisation du scanner C72 par défaut');
      _instance = C72NativeScannerService(); // Fallback vers C72
    }

    return _instance!;
  }

  /// Détecte si l'appareil est un C72
  static Future<bool> _isC72Device() async {
    try {
      // Méthode 1: Tester la disponibilité du SDK C72 natif
      final c72NativeScanner = C72NativeScannerService();
      final isNativeAvailable = await c72NativeScanner.isAvailable();

      if (isNativeAvailable) {
        await c72NativeScanner.dispose();
        return true;
      }

      // Méthode 2: Fallback vers l'ancien plugin (si nécessaire)
      final c72Scanner = C72ScannerServiceImpl();
      final isAvailable = await c72Scanner.isAvailable();
      await c72Scanner.dispose();

      return isAvailable;
    } catch (e) {
      return false;
    }
  }

  /// Force l'utilisation d'un type de scanner spécifique (pour les tests)
  static void forceScanner(ScannerService scanner) {
    _instance = scanner;
  }

  /// Réinitialise l'instance (pour les tests)
  static void reset() {
    _instance?.dispose();
    _instance = null;
  }
}

/// Implémentation pour le scanner via caméra mobile (existant)
class MobileCameraScannerService extends ScannerService {
  final StreamController<ScanResult> _scanController =
      StreamController<ScanResult>.broadcast();
  bool _isScanning = false;

  @override
  ScannerType get scannerType => ScannerType.mobileCamera;

  @override
  Future<bool> isAvailable() async {
    // La caméra est généralement disponible sur tous les appareils mobiles
    return true;
  }

  @override
  Future<void> startScan() async {
    _isScanning = true;
    // L'implémentation réelle sera dans l'UI avec MobileScanner
  }

  @override
  Future<void> stopScan() async {
    _isScanning = false;
  }

  @override
  Stream<ScanResult> get scanResults => _scanController.stream;

  @override
  bool get isScanning => _isScanning;

  @override
  Future<void> dispose() async {
    await _scanController.close();
  }

  /// Méthode pour émettre un résultat de scan (appelée depuis l'UI)
  void emitScanResult(String data) {
    if (_isScanning) {
      _scanController.add(
        ScanResult(
          data: data,
          timestamp: DateTime.now(),
          scannerType: scannerType,
          metadata: {'source': 'mobile_camera'},
        ),
      );
    }
  }
}
