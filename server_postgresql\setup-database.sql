-- Script de configuration de la base de données PostgreSQL
-- Exécuter avec : psql -U postgres -f setup-database.sql

-- Créer la base de données si elle n'existe pas
SELECT 'CREATE DATABASE qr_scanner_db'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'qr_scanner_db')\gexec

-- Créer l'utilisateur si il n'existe pas
DO
$do$
BEGIN
   IF NOT EXISTS (
      SELECT FROM pg_catalog.pg_roles
      WHERE  rolname = 'qr_user') THEN

      CREATE ROLE qr_user LOGIN PASSWORD '1234';
   END IF;
END
$do$;

-- Donner les privilèges
GRANT ALL PRIVILEGES ON DATABASE qr_scanner_db TO qr_user;

-- Afficher les informations
\l
\du
